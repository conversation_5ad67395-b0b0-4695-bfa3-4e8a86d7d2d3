use ort::{Environment, GraphOptimization<PERSON><PERSON>l, SessionBuilder, Value};
use tokenizers::Tokenizer;

fn main() -> ort::OrtResult<()> {
    // Initialize the ONNX Runtime environment
    let environment = Environment::builder().with_name("reranker").build()?;

    // Load our model
    let session = SessionBuilder::new(&environment)?
        .with_optimization_level(GraphOptimizationLevel::Level1)?
        .with_intra_threads(1)?
        .with_model_from_file("model.onnx")?;

    let tokenizer = Tokenizer::from_file("tokenizer.json").unwrap();
    let query = "What is the capital of France?";
    let passage = "Paris is the capital of France.";

    let encoding = tokenizer.encode((query, passage), true).unwrap();
    let input_ids = encoding.get_ids();
    let attention_mask = encoding.get_attention_mask();
    let token_type_ids = encoding.get_type_ids();

    // Create tensors directly using the shape and data
    let input_ids_data: Vec<i64> = input_ids.iter().map(|&x| x as i64).collect();
    let attention_mask_data: Vec<i64> = attention_mask.iter().map(|&x| x as i64).collect();
    let token_type_ids_data: Vec<i64> = token_type_ids.iter().map(|&x| x as i64).collect();

    let input_ids_value =
        Value::from_array(session.allocator(), input_ids_data, &[1, input_ids.len()])?;
    let attention_mask_value = Value::from_array(
        session.allocator(),
        attention_mask_data,
        &[1, attention_mask.len()],
    )?;
    let token_type_ids_value = Value::from_array(
        session.allocator(),
        token_type_ids_data,
        &[1, token_type_ids.len()],
    )?;

    let inputs = vec![input_ids_value, attention_mask_value, token_type_ids_value];

    // Run the model
    let outputs = session.run(inputs)?;

    // Access the 'logits' output
    let output_tensor = outputs[0].extract_tensor::<f32>()?;
    let score = output_tensor[[0, 0]]; // Access the first element of the tensor data

    println!("\nQuery: {}", query);
    println!("Passage: {}", passage);
    println!("Reranking Score: {:.4}", score);

    Ok(())
}
